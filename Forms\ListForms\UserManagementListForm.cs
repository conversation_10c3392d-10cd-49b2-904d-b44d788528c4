﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors.Repository;
using ProManage.Modules.Data.UserMasterForm;
using ProManage.Modules.Models.UserMasterForm;

namespace ProManage.Forms.ListForms
{
    public partial class UserManagementListForm : Form
    {
        #region Private Fields

        /// <summary>
        /// DataTable for grid binding
        /// </summary>
        public DataTable GridDataTable { get; set; }

        #endregion

        #region Constructor

        public UserManagementListForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// Initializes the form and loads data
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // Set form properties
                this.Text = "User Management";
                this.WindowState = FormWindowState.Maximized;

                // Initialize grid
                InitializeGrid();

                // Wire up events
                WireUpEvents();

                // Load data
                LoadUsers();

                Debug.WriteLine("UserManagementListForm initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing UserManagementListForm: {ex.Message}");
                MessageBox.Show($"Error initializing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Initializes the grid configuration
        /// </summary>
        private void InitializeGrid()
        {
            try
            {
                // Configure grid view
                gridViewUsers.OptionsView.ShowGroupPanel = false;
                gridViewUsers.OptionsView.ShowAutoFilterRow = true;
                gridViewUsers.OptionsBehavior.Editable = true; // Allow editing for checkbox
                gridViewUsers.OptionsSelection.EnableAppearanceFocusedCell = false;
                gridViewUsers.OptionsSelection.EnableAppearanceFocusedRow = true;

                // Configure columns
                ConfigureGridColumns();

                // Setup grid events for checkbox selection
                SetupGridEvents();

                Debug.WriteLine("Grid initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing grid: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Configures grid columns
        /// </summary>
        private void ConfigureGridColumns()
        {
            try
            {
                // Clear existing columns
                gridViewUsers.Columns.Clear();

                // Add checkbox column for selection (first column)
                var colSelected = gridViewUsers.Columns.AddVisible("Selected", "Select");
                colSelected.Width = 60;
                colSelected.VisibleIndex = 0;
                colSelected.OptionsColumn.AllowEdit = true;
                colSelected.ColumnEdit = new RepositoryItemCheckEdit();

                // Add data columns
                var colUserId = gridViewUsers.Columns.AddField("UserId");
                colUserId.Caption = "ID";
                colUserId.Width = 60;
                colUserId.Visible = false; // Hide ID column

                var colUsername = gridViewUsers.Columns.AddField("Username");
                colUsername.Caption = "Username";
                colUsername.Width = 120;
                colUsername.VisibleIndex = 1;
                colUsername.OptionsColumn.AllowEdit = false;

                // Configure username column as hyperlink
                var hyperlinkEdit = new DevExpress.XtraEditors.Repository.RepositoryItemHyperLinkEdit();
                hyperlinkEdit.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
                hyperlinkEdit.SingleClick = true;
                hyperlinkEdit.Click += UsernameHyperlink_Click;
                colUsername.ColumnEdit = hyperlinkEdit;

                var colFullName = gridViewUsers.Columns.AddField("FullName");
                colFullName.Caption = "Full Name";
                colFullName.Width = 200;
                colFullName.VisibleIndex = 2;
                colFullName.OptionsColumn.AllowEdit = false;

                var colEmail = gridViewUsers.Columns.AddField("Email");
                colEmail.Caption = "Email";
                colEmail.Width = 200;
                colEmail.VisibleIndex = 3;
                colEmail.OptionsColumn.AllowEdit = false;

                var colRole = gridViewUsers.Columns.AddField("Role");
                colRole.Caption = "Role";
                colRole.Width = 100;
                colRole.VisibleIndex = 4;
                colRole.OptionsColumn.AllowEdit = false;

                var colDepartment = gridViewUsers.Columns.AddField("Department");
                colDepartment.Caption = "Department";
                colDepartment.Width = 120;
                colDepartment.VisibleIndex = 5;
                colDepartment.OptionsColumn.AllowEdit = false;

                var colPhone = gridViewUsers.Columns.AddField("Phone");
                colPhone.Caption = "Phone";
                colPhone.Width = 120;
                colPhone.VisibleIndex = 6;
                colPhone.OptionsColumn.AllowEdit = false;

                var colDesignation = gridViewUsers.Columns.AddField("Designation");
                colDesignation.Caption = "Designation";
                colDesignation.Width = 150;
                colDesignation.VisibleIndex = 7;
                colDesignation.OptionsColumn.AllowEdit = false;

                var colIsActive = gridViewUsers.Columns.AddField("IsActive");
                colIsActive.Caption = "Active";
                colIsActive.Width = 80;
                colIsActive.VisibleIndex = 8;
                colIsActive.OptionsColumn.AllowEdit = false;

                var colLastLogin = gridViewUsers.Columns.AddField("LastLoginDate");
                colLastLogin.Caption = "Last Login";
                colLastLogin.Width = 120;
                colLastLogin.VisibleIndex = 9;
                colLastLogin.OptionsColumn.AllowEdit = false;

                var colCreated = gridViewUsers.Columns.AddField("CreatedDate");
                colCreated.Caption = "Created";
                colCreated.Width = 120;
                colCreated.VisibleIndex = 10;
                colCreated.OptionsColumn.AllowEdit = false;

                Debug.WriteLine("Grid columns configured successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error configuring grid columns: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Wires up event handlers
        /// </summary>
        private void WireUpEvents()
        {
            try
            {
                // Ribbon button events
                barButtonItem2.ItemClick += BtnNew_ItemClick;
                BarButtonItemEdit.ItemClick += BtnEdit_ItemClick;
                BarButtonItemDelete.ItemClick += BtnDelete_ItemClick;
                // Note: Removed Save/Cancel buttons as requested

                // Grid events
                gridViewUsers.DoubleClick += GridViewUsers_DoubleClick;

                Debug.WriteLine("Events wired up successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error wiring up events: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up grid events for checkbox single selection
        /// </summary>
        private void SetupGridEvents()
        {
            try
            {
                // Remove existing event handlers to avoid duplicates
                gridViewUsers.CellValueChanged -= GridView_CellValueChanged;
                gridViewUsers.CellValueChanging -= GridView_CellValueChanging;
                gridViewUsers.ShowingEditor -= GridView_ShowingEditor;

                // Add event handlers for checkbox single selection
                gridViewUsers.ShowingEditor += GridView_ShowingEditor;
                gridViewUsers.CellValueChanging += GridView_CellValueChanging;
                gridViewUsers.CellValueChanged += GridView_CellValueChanged;

                Debug.WriteLine("Grid events setup completed for single row selection");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up grid events: {ex.Message}");
            }
        }

        #endregion

        #region Data Operations

        /// <summary>
        /// Loads users from the database
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                Debug.WriteLine("Loading users...");

                var users = UserMasterFormRepository.GetAllUsers();

                // Create DataTable for grid binding
                CreateDataTable(users);

                // Bind to grid
                gridUsers.DataSource = GridDataTable;

                // Update button states
                UpdateButtonStates();

                Debug.WriteLine($"Loaded {users?.Count ?? 0} users");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading users: {ex.Message}");
                MessageBox.Show($"Error loading users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Creates DataTable from user list
        /// </summary>
        /// <param name="users">List of users</param>
        private void CreateDataTable(List<UserMasterFormModel> users)
        {
            try
            {
                // Create DataTable with required columns
                GridDataTable = new DataTable();

                // Add columns
                GridDataTable.Columns.Add("Selected", typeof(bool));
                GridDataTable.Columns.Add("UserId", typeof(int));
                GridDataTable.Columns.Add("Username", typeof(string));
                GridDataTable.Columns.Add("FullName", typeof(string));
                GridDataTable.Columns.Add("Email", typeof(string));
                GridDataTable.Columns.Add("Role", typeof(string));
                GridDataTable.Columns.Add("Department", typeof(string));
                GridDataTable.Columns.Add("Phone", typeof(string));
                GridDataTable.Columns.Add("Designation", typeof(string));
                GridDataTable.Columns.Add("IsActive", typeof(bool));
                GridDataTable.Columns.Add("LastLoginDate", typeof(DateTime));
                GridDataTable.Columns.Add("CreatedDate", typeof(DateTime));

                // Populate rows
                if (users != null)
                {
                    foreach (var user in users)
                    {
                        var row = GridDataTable.NewRow();
                        row["Selected"] = false; // Default to not selected
                        row["UserId"] = user.UserId;
                        row["Username"] = user.Username ?? string.Empty;
                        row["FullName"] = user.FullName ?? string.Empty;
                        row["Email"] = user.Email ?? string.Empty;
                        row["Role"] = user.Role ?? string.Empty;
                        row["Department"] = user.Department ?? string.Empty;
                        row["Phone"] = user.Phone ?? string.Empty;
                        row["Designation"] = user.Designation ?? string.Empty;
                        row["IsActive"] = user.IsActive;
                        row["LastLoginDate"] = user.LastLoginDate ?? (object)DBNull.Value;
                        row["CreatedDate"] = user.CreatedDate ?? (object)DBNull.Value;

                        GridDataTable.Rows.Add(row);
                    }
                }

                Debug.WriteLine($"DataTable created with {GridDataTable.Rows.Count} rows");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating DataTable: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Refreshes the user list
        /// </summary>
        private void RefreshUsers()
        {
            try
            {
                LoadUsers();
                Debug.WriteLine("Users refreshed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing users: {ex.Message}");
                MessageBox.Show($"Error refreshing users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles New button click
        /// </summary>
        private void BtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("New button clicked");
                OpenUserMasterForm(null); // null for new user
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button: {ex.Message}");
                MessageBox.Show($"Error creating new user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click
        /// </summary>
        private void BtnEdit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Edit button clicked");

                var selectedUser = GetSelectedUser();
                if (selectedUser != null)
                {
                    OpenUserMasterForm(selectedUser);
                }
                else
                {
                    MessageBox.Show("Please select a user to edit by checking the checkbox.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button: {ex.Message}");
                MessageBox.Show($"Error editing user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Delete button click
        /// </summary>
        private void BtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Delete button clicked");

                var selectedUser = GetSelectedUser();
                if (selectedUser != null)
                {
                    var result = MessageBox.Show(
                        $"Are you sure you want to delete user '{selectedUser.FullName}'?\n\nThis will deactivate the user account.",
                        "Confirm Delete",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        bool success = UserMasterFormRepository.DeleteUser(selectedUser.UserId);
                        if (success)
                        {
                            MessageBox.Show("User deleted successfully.", "Success",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            RefreshUsers();
                        }
                        else
                        {
                            MessageBox.Show("Failed to delete user.", "Error",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Please select a user to delete by checking the checkbox.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button: {ex.Message}");
                MessageBox.Show($"Error deleting user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles grid double click
        /// </summary>
        private void GridViewUsers_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Grid double-clicked");

                var selectedUser = GetSelectedUser();
                if (selectedUser != null)
                {
                    OpenUserMasterForm(selectedUser);
                }
                else
                {
                    // If no checkbox is selected, try to get the focused row
                    int focusedRowHandle = gridViewUsers.FocusedRowHandle;
                    if (focusedRowHandle >= 0)
                    {
                        var userId = Convert.ToInt32(gridViewUsers.GetRowCellValue(focusedRowHandle, "UserId"));
                        var user = UserMasterFormRepository.GetUserById(userId);
                        if (user != null)
                        {
                            OpenUserMasterForm(user);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DoubleClick: {ex.Message}");
                MessageBox.Show($"Error opening user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region Grid Checkbox Events

        /// <summary>
        /// Handles grid showing editor event to allow only checkbox editing
        /// </summary>
        private void GridView_ShowingEditor(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                var view = sender as GridView;
                if (view?.FocusedColumn?.FieldName != "Selected")
                {
                    e.Cancel = true; // Only allow editing of the Selected column
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ShowingEditor: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles grid cell value changing event for single selection
        /// </summary>
        private void GridView_CellValueChanging(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                var view = sender as GridView;
                if (view == null || e.Column?.FieldName != "Selected") return;

                // If checking a checkbox, uncheck all others first
                if (Convert.ToBoolean(e.Value))
                {
                    for (int i = 0; i < view.RowCount; i++)
                    {
                        if (i != e.RowHandle)
                        {
                            view.SetRowCellValue(i, "Selected", false);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CellValueChanging: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles grid cell value changed event
        /// </summary>
        private void GridView_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                var view = sender as GridView;
                if (view == null || e.Column?.FieldName != "Selected") return;

                // Update button states when selection changes
                UpdateButtonStates();

                Debug.WriteLine($"Row {e.RowHandle} selection changed to {e.Value}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CellValueChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles username hyperlink click
        /// </summary>
        private void UsernameHyperlink_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Username hyperlink clicked");

                // Get the focused row handle
                int focusedRowHandle = gridViewUsers.FocusedRowHandle;
                if (focusedRowHandle >= 0)
                {
                    // First, select the row by checking its checkbox
                    // Uncheck all other rows
                    for (int i = 0; i < gridViewUsers.RowCount; i++)
                    {
                        gridViewUsers.SetRowCellValue(i, "Selected", i == focusedRowHandle);
                    }

                    // Get the user ID and open the form
                    var userId = Convert.ToInt32(gridViewUsers.GetRowCellValue(focusedRowHandle, "UserId"));
                    var user = UserMasterFormRepository.GetUserById(userId);
                    if (user != null)
                    {
                        Debug.WriteLine($"Opening UserMasterForm for user: {user.Username} via hyperlink");
                        OpenUserMasterForm(user);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in username hyperlink click: {ex.Message}");
                MessageBox.Show($"Error opening user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets the selected user from the grid based on checkbox selection
        /// </summary>
        /// <returns>Selected user model or null if none selected</returns>
        private UserMasterFormModel GetSelectedUser()
        {
            try
            {
                if (GridDataTable == null) return null;

                // Find the selected row
                for (int i = 0; i < gridViewUsers.RowCount; i++)
                {
                    bool isSelected = Convert.ToBoolean(gridViewUsers.GetRowCellValue(i, "Selected"));
                    if (isSelected)
                    {
                        int userId = Convert.ToInt32(gridViewUsers.GetRowCellValue(i, "UserId"));
                        var user = UserMasterFormRepository.GetUserById(userId);
                        Debug.WriteLine($"Selected user: {user?.Username}");
                        return user;
                    }
                }

                Debug.WriteLine("No user selected");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting selected user: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Updates button states based on checkbox selection
        /// </summary>
        private void UpdateButtonStates()
        {
            try
            {
                bool hasSelection = GetSelectedUser() != null;

                BarButtonItemEdit.Enabled = hasSelection;
                BarButtonItemDelete.Enabled = hasSelection;

                Debug.WriteLine($"Button states updated. Has selection: {hasSelection}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating button states: {ex.Message}");
            }
        }

        /// <summary>
        /// Opens the UserMasterForm for creating or editing a user
        /// </summary>
        /// <param name="user">User to edit, or null for new user</param>
        private void OpenUserMasterForm(UserMasterFormModel user)
        {
            try
            {
                using (var userForm = new UserMasterForm())
                {
                    // Set the user for editing (null for new)
                    if (user != null)
                    {
                        userForm.SetUserForEditing(user);
                        Debug.WriteLine($"Opening UserMasterForm for editing user: {user.Username}");
                    }
                    else
                    {
                        userForm.SetNewUserMode();
                        Debug.WriteLine("Opening UserMasterForm for new user");
                    }

                    // Show as dialog
                    var result = userForm.ShowDialog(this);

                    // Refresh the list if user was saved
                    if (result == DialogResult.OK)
                    {
                        RefreshUsers();
                        Debug.WriteLine("User form closed with OK result, refreshing list");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error opening UserMasterForm: {ex.Message}");
                MessageBox.Show($"Error opening user form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
